package me.zivush.whdduel;

import org.bukkit.plugin.java.JavaPlugin;

public final class WHDDuel extends JavaPlugin {

    private ConfigManager configManager;
    private DuelManager duelManager;
    private CommandHandler commandHandler;
    private EventListener eventListener;

    @Override
    public void onEnable() {
        // Initialize managers
        configManager = new ConfigManager(this);
        duelManager = new DuelManager(this, configManager);
        commandHandler = new CommandHandler(this, configManager, duelManager);
        eventListener = new EventListener(this, configManager, duelManager);

        // Register commands
        getCommand("duel").setExecutor(commandHandler);
        getCommand("duel").setTabCompleter(commandHandler);
        getCommand("dueladmin").setExecutor(commandHandler);
        getCommand("dueladmin").setTabCompleter(commandHandler);
        getCommand("duelaccept").setExecutor(commandHandler);
        getCommand("dueldeny").setExecutor(commandHandler);

        // Register events
        getServer().getPluginManager().registerEvents(eventListener, this);

        // Log startup
        getLogger().info("WHD Duel Plugin has been enabled!");
        getLogger().info("Use /duel <player> to challenge someone to a duel");
        getLogger().info("Use /dueladmin setpos <1|2> to set arena positions");
    }

    @Override
    public void onDisable() {
        // Plugin shutdown logic
        getLogger().info("WHD Duel Plugin has been disabled!");
    }

    // Getter methods for other classes to access managers
    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DuelManager getDuelManager() {
        return duelManager;
    }
}
