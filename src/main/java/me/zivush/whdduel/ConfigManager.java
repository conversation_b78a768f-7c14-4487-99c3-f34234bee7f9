package me.zivush.whdduel;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.*;

public class ConfigManager {
    private final WHDDuel plugin;
    private FileConfiguration config;
    
    public ConfigManager(WHDDuel plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        this.config = plugin.getConfig();
    }
    
    public void saveConfig() {
        plugin.saveConfig();
    }
    
    // Arena position methods
    public Location getArenaPosition(int position) {
        String path = "arena.position" + position;
        if (!config.contains(path)) {
            return null;
        }
        
        String worldName = config.getString(path + ".world");
        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            return null;
        }
        
        double x = config.getDouble(path + ".x");
        double y = config.getDouble(path + ".y");
        double z = config.getDouble(path + ".z");
        float yaw = (float) config.getDouble(path + ".yaw");
        float pitch = (float) config.getDouble(path + ".pitch");
        
        return new Location(world, x, y, z, yaw, pitch);
    }
    
    public void setArenaPosition(int position, Location location) {
        String path = "arena.position" + position;
        config.set(path + ".world", location.getWorld().getName());
        config.set(path + ".x", location.getX());
        config.set(path + ".y", location.getY());
        config.set(path + ".z", location.getZ());
        config.set(path + ".yaw", location.getYaw());
        config.set(path + ".pitch", location.getPitch());
        saveConfig();
    }
    
    // Restricted options methods
    public Set<String> getRestrictedOptions() {
        ConfigurationSection section = config.getConfigurationSection("restricted_options");
        return section != null ? section.getKeys(false) : new HashSet<>();
    }
    
    public String getOptionDisplayName(String option) {
        return config.getString("restricted_options." + option + ".display_name", "&c" + option);
    }
    
    public String getOptionDescription(String option) {
        return config.getString("restricted_options." + option + ".description", "&7Toggle " + option);
    }
    
    public Material getOptionMaterial(String option) {
        String materialName = config.getString("restricted_options." + option + ".material", "BARRIER");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            return Material.BARRIER;
        }
    }
    
    public int getOptionSlot(String option) {
        return config.getInt("restricted_options." + option + ".slot", 10);
    }
    
    // GUI configuration methods
    public String getGuiTitle() {
        return config.getString("gui.title", "&6&lDuel Setup");
    }
    
    public int getGuiSize() {
        return config.getInt("gui.size", 27);
    }
    
    public Material getEnabledItemMaterial() {
        String materialName = config.getString("gui.enabled_item.material", "LIME_DYE");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            return Material.LIME_DYE;
        }
    }
    
    public String getEnabledItemDisplayName() {
        return config.getString("gui.enabled_item.display_name", "&a&lENABLED");
    }
    
    public List<String> getEnabledItemLore() {
        return config.getStringList("gui.enabled_item.lore");
    }
    
    public Material getDisabledItemMaterial() {
        String materialName = config.getString("gui.disabled_item.material", "RED_DYE");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            return Material.RED_DYE;
        }
    }
    
    public String getDisabledItemDisplayName() {
        return config.getString("gui.disabled_item.display_name", "&c&lDISABLED");
    }
    
    public List<String> getDisabledItemLore() {
        return config.getStringList("gui.disabled_item.lore");
    }
    
    public Material getDoneButtonMaterial() {
        String materialName = config.getString("gui.done_button.material", "EMERALD");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            return Material.EMERALD;
        }
    }
    
    public String getDoneButtonDisplayName() {
        return config.getString("gui.done_button.display_name", "&a&lSend Duel Request");
    }
    
    public List<String> getDoneButtonLore() {
        return config.getStringList("gui.done_button.lore");
    }
    
    public int getDoneButtonSlot() {
        return config.getInt("gui.done_button.slot", 22);
    }
    
    public Material getCancelButtonMaterial() {
        String materialName = config.getString("gui.cancel_button.material", "BARRIER");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            return Material.BARRIER;
        }
    }
    
    public String getCancelButtonDisplayName() {
        return config.getString("gui.cancel_button.display_name", "&c&lCancel");
    }
    
    public List<String> getCancelButtonLore() {
        return config.getStringList("gui.cancel_button.lore");
    }
    
    public int getCancelButtonSlot() {
        return config.getInt("gui.cancel_button.slot", 18);
    }
    
    public Material getFillerMaterial() {
        String materialName = config.getString("gui.filler.material", "GRAY_STAINED_GLASS_PANE");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            return Material.GRAY_STAINED_GLASS_PANE;
        }
    }
    
    public String getFillerDisplayName() {
        return config.getString("gui.filler.display_name", " ");
    }
    
    // Message methods
    public String getMessage(String key) {
        return config.getString("messages." + key, "&cMessage not found: " + key);
    }
    
    public String getMessage(String key, String placeholder, String value) {
        return getMessage(key).replace("{" + placeholder + "}", value);
    }
    
    public String getMessage(String key, Map<String, String> placeholders) {
        String message = getMessage(key);
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            message = message.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return message;
    }
    
    // Restriction methods
    public List<String> getRestrictedItems(String restriction) {
        return config.getStringList("restrictions." + restriction.toLowerCase());
    }
    
    // Timing methods
    public int getRequestExpireSeconds() {
        return config.getInt("timings.request_expire_seconds", 60);
    }
    
    public int getTeleportDelaySeconds() {
        return config.getInt("timings.teleport_delay_seconds", 3);
    }
    
    // Debug methods
    public boolean isDebugEnabled() {
        return config.getBoolean("debug.enabled", false);
    }
    
    public boolean shouldLogDuelEvents() {
        return config.getBoolean("debug.log_duel_events", false);
    }
}
