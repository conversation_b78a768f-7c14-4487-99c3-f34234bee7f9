# WHD Duel Plugin Configuration
# All messages, GUI elements, and mechanics are fully customizable

# Arena Configuration
arena:
  # Position 1 coordinates (where first player spawns)
  position1:
    world: "world"
    x: 0.0
    y: 64.0
    z: 0.0
    yaw: 0.0
    pitch: 0.0
  
  # Position 2 coordinates (where second player spawns)
  position2:
    world: "world"
    x: 10.0
    y: 64.0
    z: 0.0
    yaw: 180.0
    pitch: 0.0

# Duel Restriction Options
# These items/mechanics can be toggled ON/OFF in the pre-duel GUI
restricted_options:
  POTIONS:
    display_name: "&cDisable Potions"
    description: "&7Toggle to disable potion usage"
    material: "POTION"
    slot: 10
  ELYTRA:
    display_name: "&cDisable Elytra"
    description: "&7Toggle to disable elytra usage"
    material: "ELYTRA"
    slot: 11
  GOLDEN_APPLES:
    display_name: "&cDisable Golden Apples"
    description: "&7Toggle to disable golden apple consumption"
    material: "GOLDEN_APPLE"
    slot: 12
  ENDER_PEARLS:
    display_name: "&cDisable Ender Pearls"
    description: "&7Toggle to disable ender pearl usage"
    material: "ENDER_PEARL"
    slot: 13
  TOTEMS:
    display_name: "&cDisable Totems"
    description: "&7Toggle to disable totem usage"
    material: "TOTEM_OF_UNDYING"
    slot: 14
  SHIELDS:
    display_name: "&cDisable Shields"
    description: "&7Toggle to disable shield usage"
    material: "SHIELD"
    slot: 15
  CHORUS_FRUIT:
    display_name: "&cDisable Chorus Fruit"
    description: "&7Toggle to disable chorus fruit usage"
    material: "CHORUS_FRUIT"
    slot: 16

# GUI Configuration
gui:
  title: "&6&lDuel Setup"
  size: 27  # Must be multiple of 9 (9, 18, 27, 36, 45, 54)
  
  # Toggle states
  enabled_item:
    material: "LIME_DYE"
    display_name: "&a&lENABLED"
    lore:
      - "&7Click to disable this restriction"
  
  disabled_item:
    material: "RED_DYE"
    display_name: "&c&lDISABLED"
    lore:
      - "&7Click to enable this restriction"
  
  # Done button
  done_button:
    material: "EMERALD"
    display_name: "&a&lSend Duel Request"
    lore:
      - "&7Click to send the duel request"
      - "&7with current restrictions"
    slot: 22
  
  # Cancel button
  cancel_button:
    material: "BARRIER"
    display_name: "&c&lCancel"
    lore:
      - "&7Click to cancel duel setup"
    slot: 18
  
  # Filler items
  filler:
    material: "GRAY_STAINED_GLASS_PANE"
    display_name: " "

# Messages Configuration
messages:
  # Command messages
  no_permission: "&cYou don't have permission to use this command!"
  player_only: "&cThis command can only be used by players!"
  player_not_found: "&cPlayer '{player}' not found or not online!"
  cannot_duel_self: "&cYou cannot duel yourself!"
  
  # Duel request messages
  duel_request_sent: "&aYou sent a duel request to &e{target}&a!"
  duel_request_received: "&e{sender} &ahas challenged you to a duel!"
  duel_request_restrictions: "&7Restrictions: &f{restrictions}"
  duel_request_accept: "&a&l[ACCEPT] &r&aClick to accept the duel"
  duel_request_deny: "&c&l[DENY] &r&cClick to deny the duel"
  duel_request_denied: "&c{target} denied your duel request."
  
  # Duel state messages
  already_in_duel: "&cYou are already in a duel!"
  target_in_duel: "&c{target} is already in a duel!"
  duel_accepted: "&aYou accepted the duel request from &e{sender}&a!"
  duel_started: "&6&lDuel Started! &r&eFight!"
  duel_ended_winner: "&a&lYou won the duel!"
  duel_ended_loser: "&c&lYou lost the duel!"
  duel_ended_disconnect: "&7The duel ended because your opponent disconnected."
  
  # Admin messages
  position_set: "&aArena position {position} set to your current location!"
  position_not_set: "&cArena positions are not configured! Use /dueladmin setpos <1|2>"
  invalid_position: "&cInvalid position! Use 1 or 2."
  
  # Restriction messages
  restriction_blocked: "&cYou cannot use {item} during this duel!"
  
  # Error messages
  duel_request_expired: "&cThe duel request has expired."
  no_pending_request: "&cYou don't have any pending duel requests."
  teleport_failed: "&cFailed to teleport to arena!"

# Restriction enforcement settings
restrictions:
  # Block these items when POTIONS is disabled
  potions:
    - "POTION"
    - "SPLASH_POTION"
    - "LINGERING_POTION"
  
  # Block these items when GOLDEN_APPLES is disabled
  golden_apples:
    - "GOLDEN_APPLE"
    - "ENCHANTED_GOLDEN_APPLE"
  
  # Block these items when ENDER_PEARLS is disabled
  ender_pearls:
    - "ENDER_PEARL"
  
  # Block these items when TOTEMS is disabled
  totems:
    - "TOTEM_OF_UNDYING"
  
  # Block these items when SHIELDS is disabled
  shields:
    - "SHIELD"
  
  # Block these items when CHORUS_FRUIT is disabled
  chorus_fruit:
    - "CHORUS_FRUIT"

# Timing settings
timings:
  request_expire_seconds: 60  # How long duel requests last
  teleport_delay_seconds: 3   # Delay before teleporting to arena

# Debug settings
debug:
  enabled: false
  log_duel_events: false
