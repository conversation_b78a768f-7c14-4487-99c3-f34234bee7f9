name: WHDDuel
version: '${project.version}'
main: me.zivush.whdduel.WHDDuel
api-version: '1.20'
description: A customizable duel plugin for Minecraft servers
author: zivush

commands:
  duel:
    description: Challenge another player to a duel
    usage: /duel <player> or /duel <player># for instant duel
    permission: whdduel.duel
  dueladmin:
    description: Admin commands for duel management
    usage: /dueladmin <setpos> <1|2>
    permission: whdduel.admin
  duelaccept:
    description: Accept a pending duel request
    usage: /duelaccept
    permission: whdduel.duel
  dueldeny:
    description: Deny a pending duel request
    usage: /dueldeny
    permission: whdduel.duel

permissions:
  whdduel.duel:
    description: Allows players to initiate duels
    default: true
  whdduel.admin:
    description: Allows access to admin commands
    default: op
